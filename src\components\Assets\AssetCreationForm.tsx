'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import {
  Modal,
  Button,
  Group,
  Text,
  Stack,
  TextInput,
  Textarea,
  Select,
  Paper,
  Image,
  ActionIcon,
  Progress,
  Grid,
  Card,
  Box
} from '@mantine/core';
import { Dropzone, IMAGE_MIME_TYPE } from '@mantine/dropzone';
import { DatePickerInput } from '@mantine/dates';
import { notifications } from '@mantine/notifications';
import { IconUpload, IconX, IconPhoto, IconTrash, IconTemplate, IconPlus } from '@tabler/icons-react';
import { fetchData, insertData, uploadFile, getFileUrl } from '../../lib/supabase';
import {
  ASSET_TYPES,
  AssetType,
  AssetTemplate,
  validateAssetData,
  createDefaultAssetMetadata,
  generateAssetFilename
} from '../../lib/assets-utils';


interface AssetCreationFormProps {
  opened: boolean;
  onClose: () => void;
  onAssetCreated?: () => void;
}

interface FormData {
  title: string;
  description: string;
  asset_type: AssetType | '';
  template_id: string;
  expiry_date: Date | null;
}

export function AssetCreationForm({ opened, onClose, onAssetCreated }: AssetCreationFormProps) {
  const { data: session } = useSession();
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [templates, setTemplates] = useState<AssetTemplate[]>([]);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<AssetTemplate | null>(null);
  
  const [formData, setFormData] = useState<FormData>({
    title: '',
    description: '',
    asset_type: '',
    template_id: '',
    expiry_date: null
  });

  // Fetch templates on component mount
  useEffect(() => {
    if (opened) {
      fetchTemplates();
    }
  }, [opened]);

  // Update selected template when template_id changes
  useEffect(() => {
    if (formData.template_id && templates.length > 0) {
      const template = templates.find(t => t.id === formData.template_id);
      setSelectedTemplate(template || null);
    } else {
      setSelectedTemplate(null);
    }
  }, [formData.template_id, templates]);



  const fetchTemplates = async () => {
    try {
      const { data, error } = await fetchData('asset_templates', {
        select: '*',
        filter: [{ column: 'is_active', value: true }]
      });

      if (!error && data) {
        setTemplates(Array.isArray(data) ? data : [data]);
      }
    } catch (error) {
      console.error('Error fetching templates:', error);
    }
  };

  const handleImageUpload = (files: File[]) => {
    if (files.length === 0) return;

    const file = files[0];
    setSelectedImage(file);

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  const removeImage = () => {
    setSelectedImage(null);
    setImagePreview(null);
  };

  const handleSubmit = async () => {
    if (!session?.user?.email) {
      notifications.show({
        title: 'Error',
        message: 'You must be logged in to create assets',
        color: 'red',
      });
      return;
    }

    if (!selectedImage) {
      notifications.show({
        title: 'Error',
        message: 'Please upload an image for the asset',
        color: 'red',
      });
      return;
    }

    // Validate form data
    let expiryDateISO = null;
    if (formData.expiry_date) {
      try {
        // Ensure we have a proper Date object
        const dateObj = formData.expiry_date instanceof Date
          ? formData.expiry_date
          : new Date(formData.expiry_date);

        // Check if the date is valid
        if (!isNaN(dateObj.getTime())) {
          expiryDateISO = dateObj.toISOString();
        }
      } catch (error) {
        console.error('Error processing expiry date:', error);
      }
    }

    const assetData = {
      title: formData.title,
      description: formData.description,
      asset_type: formData.asset_type as AssetType,
      template_id: formData.template_id || null,
      issuer_odude_name: '', // Will be set from user's primary ODude name
      issuer_email: session.user.email,
      expiry_date: expiryDateISO,
      image_url: '', // Will be set after upload
      metadata: {}
    };

    // Set default metadata based on asset type
    assetData.metadata = createDefaultAssetMetadata(formData.asset_type as AssetType);

    try {
      setLoading(true);
      setUploading(true);
      setUploadProgress(0);

      // Get user's primary ODude name from ownership
      const response = await fetch('/api/owner/access-check');
      if (!response.ok) {
        notifications.show({
          title: 'Error',
          message: 'Failed to check ownership permissions',
          color: 'red',
        });
        return;
      }

      const ownershipData = await response.json();
      if (!ownershipData.isOwner || !ownershipData.ownedPrimaryNames || ownershipData.ownedPrimaryNames.length === 0) {
        notifications.show({
          title: 'Error',
          message: 'You must own at least one primary name to create assets. Please contact an administrator.',
          color: 'red',
        });
        return;
      }

      // Use the first owned primary name as the issuer ODude name
      const primaryName = ownershipData.ownedPrimaryNames[0].name;
      assetData.issuer_odude_name = `${session.user.email.split('@')[0]}@${primaryName}`;

      // Upload image first
      setUploadProgress(25);
      const filename = generateAssetFilename(formData.title, formData.asset_type as AssetType);
      const filePath = `assets/${filename}.${selectedImage.name.split('.').pop()}`;

      const { error: uploadError } = await uploadFile('images', filePath, selectedImage);
      if (uploadError) throw uploadError;

      setUploadProgress(50);

      // Get public URL and set it in asset data
      const imageUrl = getFileUrl('images', filePath);
      assetData.image_url = imageUrl;

      // Now validate the asset data with all fields properly set including image_url
      const validationErrors = validateAssetData(assetData);
      if (validationErrors.length > 0) {
        notifications.show({
          title: 'Validation Error',
          message: validationErrors.join(', '),
          color: 'red',
        });
        return;
      }

      setUploadProgress(75);

      // Insert asset into database
      const { error: insertError } = await insertData('assets', assetData);
      if (insertError) throw insertError;

      setUploadProgress(100);

      notifications.show({
        title: 'Success',
        message: 'Asset created successfully',
        color: 'green',
      });

      // Reset form
      setFormData({
        title: '',
        description: '',
        asset_type: '',
        template_id: '',
        expiry_date: null
      });
      removeImage();
      
      if (onAssetCreated) {
        onAssetCreated();
      }
      
      onClose();

    } catch (error) {
      console.error('Error creating asset:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to create asset. Please try again.',
        color: 'red',
      });
    } finally {
      setLoading(false);
      setUploading(false);
      setUploadProgress(0);
    }
  };

  const filteredTemplates = templates.filter(template => 
    !formData.asset_type || template.asset_type === formData.asset_type
  );

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title="Create New Asset"
      size="lg"
      centered
    >
      <Stack gap="md">
        {/* Basic Information */}
        <Grid>
          <Grid.Col span={12}>
            <TextInput
              label="Asset Title"
              placeholder="Enter asset title"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              required
            />
          </Grid.Col>
          
          <Grid.Col span={6}>
            <Select
              label="Asset Type"
              placeholder="Select asset type"
              data={ASSET_TYPES.map(type => ({ value: type, label: type }))}
              value={formData.asset_type}
              onChange={(value) => setFormData(prev => ({ ...prev, asset_type: value as AssetType || '' }))}
              required
            />
          </Grid.Col>

          <Grid.Col span={6}>
            <DatePickerInput
              label="Expiry Date (Optional)"
              placeholder="Select expiry date"
              value={formData.expiry_date}
              onChange={(date) => setFormData(prev => ({ ...prev, expiry_date: date as Date | null }))}
              minDate={new Date()}
            />
          </Grid.Col>
        </Grid>

        <Textarea
          label="Description"
          placeholder="Enter asset description"
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          minRows={3}
        />

        {/* Template Selection */}
        {filteredTemplates.length > 0 && (
          <div>
            <Text size="sm" fw={500} mb="xs">Template (Optional)</Text>
            <Grid>
              {filteredTemplates.map((template) => (
                <Grid.Col span={6} key={template.id}>
                  <Card
                    padding="sm"
                    withBorder
                    style={{
                      cursor: 'pointer',
                      borderColor: formData.template_id === template.id ? '#4F46E5' : undefined
                    }}
                    onClick={() => setFormData(prev => ({ 
                      ...prev, 
                      template_id: prev.template_id === template.id ? '' : template.id 
                    }))}
                  >
                    <Group justify="space-between">
                      <div>
                        <Text size="sm" fw={500}>{template.name}</Text>
                        <Text size="xs" c="dimmed">{template.description}</Text>
                      </div>
                      <IconTemplate size={20} />
                    </Group>
                  </Card>
                </Grid.Col>
              ))}
            </Grid>
          </div>
        )}

        {/* Image Upload */}
        <div>
          <Text size="sm" fw={500} mb="xs">Asset Image *</Text>
          {!imagePreview ? (
            <Dropzone
              onDrop={handleImageUpload}
              accept={IMAGE_MIME_TYPE}
              multiple={false}
              disabled={uploading}
            >
              <Group justify="center" gap="xl" style={{ minHeight: 120, pointerEvents: 'none' }}>
                <Dropzone.Accept>
                  <IconUpload size={50} color="green" />
                </Dropzone.Accept>
                <Dropzone.Reject>
                  <IconX size={50} color="red" />
                </Dropzone.Reject>
                <Dropzone.Idle>
                  <IconPhoto size={50} color="gray" />
                </Dropzone.Idle>
                <div>
                  <Text size="xl" inline>Drag image here or click to select</Text>
                  <Text size="sm" c="dimmed" inline mt={7}>
                    PNG, JPG, SVG files are accepted
                  </Text>
                </div>
              </Group>
            </Dropzone>
          ) : (
            <Paper withBorder p="md">
              <Group justify="space-between">
                <Image
                  src={imagePreview}
                  alt="Asset preview"
                  width={100}
                  height={100}
                  fit="cover"
                />
                <ActionIcon color="red" onClick={removeImage}>
                  <IconTrash size={16} />
                </ActionIcon>
              </Group>
            </Paper>
          )}
        </div>



        {/* Template Preview */}
        {selectedTemplate && imagePreview && (
          <Paper withBorder p="md">
            <Text fw={500} mb="md">Preview</Text>
            <div style={{ position: 'relative', display: 'inline-block' }}>
              {/* Template SVG as background */}
              <div
                style={{
                  position: 'relative',
                  width: '300px',
                  height: '200px',
                  backgroundImage: selectedTemplate.preview_url ? `url(${selectedTemplate.preview_url})` : 'none',
                  backgroundSize: 'contain',
                  backgroundRepeat: 'no-repeat',
                  backgroundPosition: 'center',
                  border: '1px solid #E5E7EB',
                  borderRadius: '8px'
                }}
              >
                {/* Asset image overlay */}
                <div
                  style={{
                    position: 'absolute',
                    top: '30%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    width: '120px',
                    height: '80px',
                    borderRadius: '4px',
                    overflow: 'hidden'
                  }}
                >
                  <Image
                    src={imagePreview}
                    alt="Asset preview"
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover'
                    }}
                  />
                </div>
              </div>
            </div>
          </Paper>
        )}

        {/* Upload Progress */}
        {uploading && (
          <Box>
            <Text size="sm" mb="xs">Uploading asset...</Text>
            <Progress value={uploadProgress} />
          </Box>
        )}

        {/* Actions */}
        <Group justify="flex-end">
          <Button variant="outline" onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit} 
            loading={loading}
            leftSection={<IconPlus size={16} />}
          >
            Create Asset
          </Button>
        </Group>
      </Stack>
    </Modal>
  );
}
