'use client';
import { IconBookmark, IconRefresh, IconPhotoBitcoin, IconMail, IconExternalLink, IconPhoneCall, IconNotes, IconCopy, IconMapPin, IconInfoCircle, IconTrophy } from '@tabler/icons-react';
import { Avatar, Group, Text, Card, Image, ActionIcon, Modal, Tooltip, Anchor, Badge, Code, Stack } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import classes from './contact.module.css';
import { useState, useEffect } from 'react';
import { fetchData, updateData } from 'src/lib/supabase';
import { useSession } from "next-auth/react"
import { checkIfBookmarked, toggleBookmark, cleanupBookmarksForNonExistentContact } from 'src/lib/bookmark';
import { fetchDomain, fetchNFTowner, getImage, ImageData } from 'src/lib/common';
import { useFlyingPoints } from '../Animations/FlyingPoints';
import { getNotesData, hasNotesData } from 'src/lib/database-utils';
import { insertContactData, prepareContactData } from 'src/components/Search';
import { NAME_SLOTS } from 'src/lib/config';

interface UserInfo {
  website: string;
  profile: string;
  email: string;
  phone?: string;
  image?: string;
  description?: string;
  profile_email?: string;
  images?: ImageData;
  minted?: string;
  notes?: Record<string, string> | null;
  extra?: Record<string, string> | null;
}


export function ContactCard({ name }: { name: string }) {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshLoading, setRefreshLoading] = useState(false);
  const [bookmarkLoading, setBookmarkLoading] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [nftModalOpened, setNftModalOpened] = useState(false);
  const [avatarModalOpened, setAvatarModalOpened] = useState(false);
  const [notesModalOpened, setNotesModalOpened] = useState(false);
  const [disclaimerModalOpened, setDisclaimerModalOpened] = useState(false);
  const { data: session } = useSession();
  const { showFlyingPoints, FlyingPointsContainer } = useFlyingPoints();

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const { data } = await fetchData<UserInfo>('contact', {
          select: 'profile, email, image, profile_email, description, images, website, phone, minted, notes, extra',
          filter: [{ column: 'name', value: name.toLowerCase() }],
          single: true
        });
        //console.log('User data from Supabase:', data);
        const userData = Array.isArray(data) ? data[0] : data;
        setUserInfo(userData);

        // Check if this contact is bookmarked
        if (session?.user?.email) {
          const bookmarked = await checkIfBookmarked(name.toLowerCase(), session.user.email);
          setIsBookmarked(bookmarked);
        }

        // If user is not found, clean up any existing bookmarks
        if (!userData) {
          await cleanupBookmarksForNonExistentContact(name.toLowerCase());
        }

      } catch (error) {
        console.error('Error fetching user data:', error);
        // Also clean up bookmarks on error (user likely doesn't exist)
        await cleanupBookmarksForNonExistentContact(name);
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, [name, session?.user?.email]);

  const handleBookmarkClick = async () => {
    if (bookmarkLoading) return; // Prevent multiple clicks

    setBookmarkLoading(true);
    try {
      const newBookmarkStatus = await toggleBookmark(
        name,
        isBookmarked,
        session?.user?.email,
        (points) => {
          // Show flying points animation
          showFlyingPoints(points);
        }
      );
      setIsBookmarked(newBookmarkStatus);
    } catch (error) {
      console.error('Error toggling bookmark:', error);
    } finally {
      setBookmarkLoading(false);
    }
  };

  const handleRefreshClick = async () => {
    setRefreshLoading(true);
    try {
      // Fetch fresh data from external API
      const jsonData = await fetchDomain(name);

      if (jsonData.error) {
        notifications.show({
          title: 'Error',
          message: jsonData.error,
          color: 'red',
        });
        return;
      }

      // Prepare data for database
      const contact = prepareContactData(jsonData);
      // Ensure name is lowercase
      contact.name = contact.name.toLowerCase();

      // Ensure extra field is included (for backward compatibility)
      if (!contact.hasOwnProperty('extra')) {
        contact.extra = null;
      }

      // Filter out fields that don't exist in the database schema
      // Remove 'attributes' and any other non-database fields
      const validDatabaseFields = [
        'name', 'image', 'description', 'uri', 'profile', 'email', 'website',
        'phone', 'tg_bot', 'notes', 'web2', 'web3', 'links', 'images',
        'social', 'crypto', 'extra', 'profile_email', 'minted'
      ];

      const filteredContact = Object.keys(contact)
        .filter(key => validDatabaseFields.includes(key))
        .reduce((obj: any, key) => {
          obj[key] = (contact as any)[key];
          return obj;
        }, {});

      // Update existing record instead of delete and insert
      const updateResult = await updateData(
        'contact',
        filteredContact,
        { column: 'name', value: name.toLowerCase() }
      );
      console.log('Record updated:', updateResult);

      if (updateResult.error) {
        // If update fails (record might not exist), try to insert
        console.log('Update failed, attempting insert:', updateResult.error);
        const insertResult = await insertContactData(filteredContact);

        if (insertResult.error) {
          notifications.show({
            title: 'Error',
            message: 'Failed to update record',
            color: 'red',
          });
          return;
        } else {
          console.log('Record inserted successfully:', insertResult);
        }
      }

      notifications.show({
        title: 'Success',
        message: 'Record updated successfully',
        color: 'green',
      });

      // Check NFT owner and update minted column
      try {
        const nftOwnerAddress = await fetchNFTowner(name);
        if (nftOwnerAddress) {
          // Update the minted column with the NFT owner address
          const mintedUpdateResult = await updateData(
            'contact',
            { minted: nftOwnerAddress },
            { column: 'name', value: name.toLowerCase() }
          );

          if (mintedUpdateResult.error) {
            console.error('Failed to update minted column:', mintedUpdateResult.error);
          } else {
            // Show separate notification about NFT status
            notifications.show({
              title: 'NFT Found',
              message: `This name is already minted on blockchain. Owner: ${nftOwnerAddress}`,
              color: 'blue',
            });
          }
        }
      } catch (nftError) {
        console.error('Error checking NFT owner:', nftError);
      }

      // Refresh the local state by fetching updated data
      const { data } = await fetchData<UserInfo>('contact', {
        select: 'profile, email, image, profile_email, description, images, website, phone, minted, notes, extra',
        filter: [{ column: 'name', value: name.toLowerCase() }],
        single: true
      });
      const userData = Array.isArray(data) ? data[0] : data;
      setUserInfo(userData);

    } catch (error) {
      console.error('Update operation failed:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to update record',
        color: 'red',
      });
    } finally {
      setRefreshLoading(false);
    }
  };

  const handleNftClick = () => {
    setNftModalOpened(true);
  };

  const handleAvatarClick = () => {
    setAvatarModalOpened(true);
  };

  const handleNotesClick = () => {
    setNotesModalOpened(true);
  };

  const handleDisclaimerClick = () => {
    setDisclaimerModalOpened(true);
  };

  const handleCopyNotes = async () => {
    if (userInfo?.notes) {
      try {
        const notesData = getNotesData(userInfo);
        const allNotes = Object.entries(notesData)
          .map(([title, content]) => `${title}:\n${content}`)
          .join('\n\n');

        await navigator.clipboard.writeText(allNotes);
        notifications.show({
          title: 'Copied!',
          message: 'All notes copied to clipboard',
          color: 'green',
        });
      } catch (error) {
        notifications.show({
          title: 'Error',
          message: 'Failed to copy notes',
          color: 'red',
        });
      }
    }
  };

  const handleCopyIndividualNote = async (title: string, content: string) => {
    try {
      // Only copy the content, not the title
      await navigator.clipboard.writeText(content);
      notifications.show({
        title: 'Copied!',
        message: `"${title}" note copied to clipboard`,
        color: 'green',
      });
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: 'Failed to copy note',
        color: 'red',
      });
    }
  };

  const handleCopyNameUrl = async () => {
    try {
      const url = `https://odude.com/${name}`;
      await navigator.clipboard.writeText(url);
      notifications.show({
        title: 'Copied!',
        message: 'Profile URL copied to clipboard',
        color: 'green',
      });
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: 'Failed to copy URL',
        color: 'red',
      });
    }
  };

  // Check if user has any images
  const hasImages = () => {
    return !!(userInfo?.image && userInfo.image.trim() !== '');
  };

  // Check if user has notes
  const hasNotes = () => {
    return hasNotesData(userInfo || {});
  };

  // Check if user has location coordinates
  const hasLocation = () => {
    if (!userInfo?.extra) return false;
    try {
      const extraData = typeof userInfo.extra === 'string'
        ? JSON.parse(userInfo.extra)
        : userInfo.extra;
      return !!(extraData?.map && extraData.map.trim() !== '');
    } catch (e) {
      return false;
    }
  };

  // Handle map click to open Google Maps
  const handleMapClick = () => {
    if (!userInfo?.extra) return;

    try {
      const extraData = typeof userInfo.extra === 'string'
        ? JSON.parse(userInfo.extra)
        : userInfo.extra;

      if (extraData?.map) {
        const coordinates = extraData.map.trim();
        const [lat, lng] = coordinates.split(',');

        if (lat && lng) {
          // Open Google Maps with the coordinates
          const googleMapsUrl = `https://www.google.com/maps?q=${lat},${lng}`;
          window.open(googleMapsUrl, '_blank');
        } else {
          notifications.show({
            title: 'Error',
            message: 'Invalid coordinates format',
            color: 'red',
          });
        }
      }
    } catch (e) {
      notifications.show({
        title: 'Error',
        message: 'Failed to parse location data',
        color: 'red',
      });
    }
  };

  // Check if the primary name (part after @) is not in NAME_SLOTS
  const shouldShowRefreshButton = () => {
    if (!userInfo?.image || userInfo.image.trim() === '') return false;

    const nameParts = name.split('@');
    if (nameParts.length !== 2) return false;

    const primaryName = nameParts[1];
    return !NAME_SLOTS.includes(primaryName);
  };

  if (loading) return <div>Loading...</div>;
  if (!userInfo) return <div>User not found : {name}</div>;
  return (
    <>
      <Card withBorder padding="lg" radius="md" className={classes.card}>


        <Card.Section
          h={150}
          style={{
            backgroundImage:
              `url(${getImage(userInfo.images, 2)})`,
          }}
        />
        <Avatar
          src={getImage(userInfo.images, 1)}
          size={80}
          radius={80}
          mx="auto"
          mt={-30}
          className={classes.avatar}
          onClick={handleAvatarClick}
          style={{ cursor: 'pointer' }}
        />


        <Text ta="center" fz="lg" fw={700} mt="sm"   variant="gradient" gradient={{ from: 'blue', to: 'cyan', deg: 90 }}>
          {userInfo.profile}
        </Text>
        <Text ta="center" fz="sm" c="dimmed">
          {userInfo.description}
        </Text>

        {/* Contact Information - Single line with responsive wrapping */}
        {(userInfo.email?.trim() || userInfo.website?.trim() || userInfo.phone?.trim()) && (
          <Group gap="md" justify="center" mt="sm" wrap="wrap">
            {/* Email */}
            {userInfo.email && userInfo.email.trim() !== '' && (
              <Group gap="xs" wrap="nowrap">
                <IconMail size={16} />
                <Anchor href={`mailto:${userInfo.email}`} size="sm">
                  {userInfo.email}
                </Anchor>
              </Group>
            )}

            {/* Website */}
            {userInfo.website && userInfo.website.trim() !== '' && (
              <Group gap="xs" wrap="nowrap">
                <IconExternalLink size={14} />
                <Anchor href={userInfo.website} target="_blank" rel="noopener noreferrer" size="sm">
                  {userInfo.website}
                </Anchor>
              </Group>
            )}

            {/* Phone Numbers */}
            {userInfo.phone && userInfo.phone.trim() !== '' && (
              <Group gap="xs" wrap="wrap">
                <IconPhoneCall size={16} />
                {userInfo.phone.split(/[,\s]+/).filter(phone => phone.trim()).map((phone, index) => (
                  <Anchor key={index} href={`tel:${phone.trim()}`} size="sm">
                    {phone.trim()}
                  </Anchor>
                ))}
              </Group>
            )}
          </Group>
        )}

        <Card.Section className={classes.footer}>
          <Group justify="space-between">
            <Group gap="xs" align="center">
              <Code
                color="var(--mantine-color-yellow-light)"
                onClick={handleCopyNameUrl}
                style={{ cursor: 'pointer' }}
              >
                {name}
              </Code>
              <Tooltip label="Information disclaimer" withArrow>
                <ActionIcon
                  variant="subtle"
                  color="gray"
                  size="sm"
                  onClick={handleDisclaimerClick}
                  style={{ cursor: 'pointer' }}
                >
                  <IconInfoCircle size={14} />
                </ActionIcon>
              </Tooltip>
              {userInfo?.minted && userInfo.minted.trim() !== '' && (
                <Tooltip label="Blockchain verified" withArrow>
                  <Badge variant="filled" color="green" size="sm">
                    verified
                  </Badge>
                </Tooltip>
              )}
            </Group>
            <Group gap={3}>
              {/* Only show bookmark button if user is not the owner of this contact */}
              {session?.user?.email !== userInfo?.profile_email && (
                <Tooltip label={bookmarkLoading ? "Processing..." : (isBookmarked ? "Remove Bookmark" : "Add Bookmark")} withArrow>
                  <ActionIcon
                    variant="subtle"
                    color="gray"
                    onClick={handleBookmarkClick}
                    disabled={bookmarkLoading}
                    loading={bookmarkLoading}
                    style={{ cursor: bookmarkLoading ? 'not-allowed' : 'pointer' }}
                  >
                    <IconBookmark
                      size={50}
                      color={isBookmarked ? "var(--mantine-color-yellow-6)" : "var(--mantine-color-gray-6)"}
                      stroke={1.5}
                      fill={isBookmarked ? "var(--mantine-color-yellow-6)" : "none"}
                    />
                  </ActionIcon>
                </Tooltip>
              )}
              <Tooltip label="View Assets" withArrow>
                <ActionIcon
                  variant="subtle"
                  color="gray"
                  onClick={() => window.open(`/profile/assets/${name}`, '_blank')}
                  style={{ cursor: 'pointer' }}
                >
                  <IconTrophy
                    size={50}
                    color="var(--mantine-color-orange-6)"
                    stroke={1.5}
                  />
                </ActionIcon>
              </Tooltip>
              {hasNotes() && (
                <Tooltip label="View Notes" withArrow>
                  <ActionIcon
                    variant="subtle"
                    color="gray"
                    onClick={handleNotesClick}
                    style={{ cursor: 'pointer' }}
                  >
                    <IconNotes
                      size={50}
                      color="var(--mantine-color-blue-6)"
                      stroke={1.5}
                    />
                  </ActionIcon>
                </Tooltip>
              )}
              {hasLocation() && (
                <Tooltip label="Open Location in Maps" withArrow>
                  <ActionIcon
                    variant="subtle"
                    color="gray"
                    onClick={handleMapClick}
                    style={{ cursor: 'pointer' }}
                  >
                    <IconMapPin
                      size={50}
                      color="var(--mantine-color-red-6)"
                      stroke={1.5}
                    />
                  </ActionIcon>
                </Tooltip>
              )}
              {hasImages() && (
                <Tooltip label="View QR Code" withArrow>
                  <ActionIcon
                    variant="subtle"
                    color="gray"
                    onClick={handleNftClick}
                    style={{ cursor: 'pointer' }}
                  >
                    <IconPhotoBitcoin
                      size={50}
                      color="var(--mantine-color-green-6)"
                      stroke={1.5}
                    />
                  </ActionIcon>
                </Tooltip>
              )}
              {shouldShowRefreshButton() && (
                <Tooltip label="Refresh Contact Data" withArrow>
                  <ActionIcon
                    variant="subtle"
                    color="gray"
                    onClick={handleRefreshClick}
                    loading={refreshLoading}
                    style={{ cursor: 'pointer' }}
                  >
                    <IconRefresh size={20} color="var(--mantine-color-blue-6)" stroke={1.5} />
                  </ActionIcon>
                </Tooltip>
              )}
            </Group>
          </Group>
        </Card.Section>
      </Card>

      {/* NFT Image Modal */}
      <Modal
        opened={nftModalOpened}
        onClose={() => setNftModalOpened(false)}
        title={
          <Group gap="xs" align="center">
            <Text>NFT Image</Text>
            {userInfo?.minted && userInfo.minted.trim() !== '' && (
              <Tooltip label="Blockchain verified" withArrow>
                <Badge variant="filled" color="green" size="sm">
                  verified
                </Badge>
              </Tooltip>
            )}
          </Group>
        }
        size="lg"
        centered
      >
        <div style={{ textAlign: 'center' }}>
          <Image
            src={userInfo?.image}
            alt="NFT Image"
            style={{
              maxWidth: '100%',
              maxHeight: '400px',
              objectFit: 'contain'
            }}
          />
        </div>
      </Modal>

      {/* Avatar Image Modal */}
      <Modal
        opened={avatarModalOpened}
        onClose={() => setAvatarModalOpened(false)}
        title={
          <Group gap="xs" align="center">
            <Text>{userInfo?.profile}</Text>
            {userInfo?.minted && userInfo.minted.trim() !== '' && (
              <Tooltip label="Blockchain verified" withArrow>
                <Badge variant="filled" color="green" size="sm">
                  verified
                </Badge>
              </Tooltip>
            )}
          </Group>
        }
        size="lg"
        centered
      >
        <div style={{ textAlign: 'center' }}>
          <Image
            src={getImage(userInfo?.images, 1)}
            alt="Profile Image"
            style={{
              maxWidth: '100%',
              maxHeight: '500px',
              objectFit: 'contain'
            }}
          />
        </div>
      </Modal>

      {/* Notes Modal */}
      <Modal
        opened={notesModalOpened}
        onClose={() => setNotesModalOpened(false)}
        title={
          <Group gap="xs" align="center" justify="space-between" style={{ width: '100%' }}>
            <Group gap="xs" align="center">
              <IconNotes size={20} />
              <Text>Notes - {userInfo?.profile}</Text>
            </Group>
            <Tooltip label="Copy Notes" withArrow>
              <ActionIcon
                variant="subtle"
                color="blue"
                onClick={handleCopyNotes}
                style={{ cursor: 'pointer' }}
              >
                <IconCopy size={16} />
              </ActionIcon>
            </Tooltip>
          </Group>
        }
        size="md"
        centered
      >
        <Card withBorder padding="lg" radius="md" style={{ minHeight: '100px' }}>
          {(() => {
            const notesData = getNotesData(userInfo || {});
            const noteEntries = Object.entries(notesData);

            if (noteEntries.length === 0) {
              return (
                <Text size="sm" c="dimmed" ta="center">
                  No notes available
                </Text>
              );
            }

            return (
              <Stack gap="md">
                {noteEntries.map(([title, content], index) => (
                  <div key={index}>
                    <Group gap="xs" align="center" mb="xs">
                      <Text fw={600} size="sm" c="blue">
                        {title}
                      </Text>
                      <Tooltip label={`Copy "${title}" note`} withArrow>
                        <ActionIcon
                          variant="subtle"
                          color="gray"
                          size="sm"
                          onClick={() => handleCopyIndividualNote(title, content)}
                          style={{ cursor: 'pointer' }}
                        >
                          <IconCopy size={14} />
                        </ActionIcon>
                      </Tooltip>
                    </Group>
                    <Text
                      size="sm"
                      style={{
                        whiteSpace: 'pre-wrap',
                        lineHeight: '1.6',
                        fontFamily: 'monospace',
                        backgroundColor: 'light-dark(var(--mantine-color-gray-0), var(--mantine-color-dark-6))',
                        color: 'light-dark(var(--mantine-color-dark-9), var(--mantine-color-gray-0))',
                        padding: '8px',
                        borderRadius: '4px',
                        border: '1px solid light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4))'
                      }}
                    >
                      {content}
                    </Text>
                  </div>
                ))}
              </Stack>
            );
          })()}
        </Card>
      </Modal>

      {/* Disclaimer Modal */}
      <Modal
        opened={disclaimerModalOpened}
        onClose={() => setDisclaimerModalOpened(false)}
        title="Information Disclaimer"
        size="md"
        centered
      >
        <Stack gap="md">
          <Text size="sm">
            <strong>Important Notice:</strong> The information displayed on this profile page may be inaccurate, outdated, or incomplete.
          </Text>
          <Text size="sm">
            This profile information is provided as-is and should not be relied upon for important decisions without independent verification.
          </Text>
          <Text size="sm">
            <strong>Please note:</strong>
          </Text>
          <ul style={{ margin: 0, paddingLeft: '20px' }}>
            <li><Text size="sm">Contact details may be outdated or incorrect</Text></li>
            <li><Text size="sm">Social media links and other information may not be current</Text></li>
            <li><Text size="sm">Profile data is user-generated and not verified by ODude</Text></li>
            <li><Text size="sm">Always verify information independently before taking action</Text></li>
          </ul>
          <Text size="sm" c="dimmed">
            If you notice incorrect information, please use the "Report Profile" link in the footer to notify us.
          </Text>
        </Stack>
      </Modal>

      {/* Flying Points Animation Container */}
      <FlyingPointsContainer />
    </>
  );
}